/**
 * Google Sheets integration utilities with security measures
 *
 * This module handles sending waitlist form data to Google Sheets
 * using Google Apps Script as a web app endpoint with rate limiting,
 * input validation, and other security measures.
 */

export interface WaitlistSubmission {
  email: string;
  firstName: string;
  company?: string;
  role: string;
  useCase: string;
  dataVolume?: string;
  source?: string;
  consent: boolean;
  timestamp: string;
}

// Client-side rate limiting configuration
const RATE_LIMIT_KEY = 'waitlist_submissions';
const MAX_SUBMISSIONS_PER_HOUR = 5; // Increased from 3 to reduce false positives
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Check if the user has exceeded the client-side rate limit
 */
function checkClientRateLimit(): { allowed: boolean; timeUntilReset?: number } {
  try {
    const now = Date.now();
    const stored = localStorage.getItem(RATE_LIMIT_KEY);

    if (!stored) {
      return { allowed: true };
    }

    const submissions = JSON.parse(stored);

    // Filter out submissions older than the rate limit window
    const recentSubmissions = submissions.filter(
      (timestamp: number) => now - timestamp < RATE_LIMIT_WINDOW
    );

    if (recentSubmissions.length >= MAX_SUBMISSIONS_PER_HOUR) {
      const oldestSubmission = Math.min(...recentSubmissions);
      const timeUntilReset = RATE_LIMIT_WINDOW - (now - oldestSubmission);

      return {
        allowed: false,
        timeUntilReset: Math.ceil(timeUntilReset / (60 * 1000)) // minutes
      };
    }

    return { allowed: true };

  } catch (error) {
    console.error('Error checking client rate limit:', error);
    return { allowed: true }; // Allow if check fails
  }
}

/**
 * Record a submission timestamp for rate limiting
 */
function recordSubmission(): void {
  try {
    const now = Date.now();
    const stored = localStorage.getItem(RATE_LIMIT_KEY);

    let submissions = [];
    if (stored) {
      submissions = JSON.parse(stored);
    }

    // Add current submission and clean old ones
    submissions.push(now);
    const recentSubmissions = submissions.filter(
      (timestamp: number) => now - timestamp < RATE_LIMIT_WINDOW
    );

    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify(recentSubmissions));

    // Debug logging
    console.log('Recorded submission. Recent submissions count:', recentSubmissions.length);

  } catch (error) {
    console.error('Error recording submission:', error);
  }
}

/**
 * Debug function to check current rate limit status
 */
export const debugRateLimit = (): void => {
  try {
    const rateLimitCheck = checkClientRateLimit();
    const stored = localStorage.getItem(RATE_LIMIT_KEY);
    const submissions = stored ? JSON.parse(stored) : [];

    console.log('Rate Limit Debug Info:', {
      allowed: rateLimitCheck.allowed,
      timeUntilReset: rateLimitCheck.timeUntilReset,
      totalSubmissions: submissions.length,
      maxAllowed: MAX_SUBMISSIONS_PER_HOUR,
      rateLimitWindow: RATE_LIMIT_WINDOW / (60 * 1000) + ' minutes',
      submissions: submissions.map((ts: number) => new Date(ts).toLocaleString())
    });
  } catch (error) {
    console.error('Error debugging rate limit:', error);
  }
};

/**
 * Clear rate limit data (for debugging/testing)
 */
export const clearRateLimit = (): void => {
  try {
    localStorage.removeItem(RATE_LIMIT_KEY);
    console.log('Rate limit data cleared');
  } catch (error) {
    console.error('Error clearing rate limit:', error);
  }
};

/**
 * Test if the Google Apps Script URL is accessible
 */
export const testGoogleScriptUrl = async (): Promise<void> => {
  const GOOGLE_SCRIPT_URL = import.meta.env.VITE_GOOGLE_SCRIPT_URL;

  if (!GOOGLE_SCRIPT_URL) {
    console.error('❌ VITE_GOOGLE_SCRIPT_URL not set');
    return;
  }

  console.log('🔍 Testing Google Apps Script URL accessibility...');

  try {
    // First, try a simple GET request to see if the URL is reachable
    const response = await fetch(GOOGLE_SCRIPT_URL, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    console.log('GET Response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    const text = await response.text();
    console.log('GET Response body:', text.substring(0, 200));

    if (response.ok) {
      console.log('✅ URL is accessible');
    } else {
      console.log('⚠️ URL accessible but returned error status');
    }

  } catch (error) {
    console.error('❌ URL not accessible:', error);
    console.log('💡 This suggests:');
    console.log('   1. The Google Apps Script is not deployed as a web app');
    console.log('   2. The deployment permissions are incorrect');
    console.log('   3. The URL in your .env file is wrong');
  }
};

/**
 * Test Google Apps Script connection
 */
export const testGoogleScriptConnection = async (): Promise<void> => {
  const GOOGLE_SCRIPT_URL = import.meta.env.VITE_GOOGLE_SCRIPT_URL;
  const API_KEY = import.meta.env.VITE_GOOGLE_SCRIPT_API_KEY;

  console.log('🔍 Testing Google Apps Script Connection...');
  console.log('Raw Environment Variables:', {
    VITE_GOOGLE_SCRIPT_URL: import.meta.env.VITE_GOOGLE_SCRIPT_URL ? '[HIDDEN - URL SET]' : 'NOT SET',
    VITE_GOOGLE_SCRIPT_API_KEY: import.meta.env.VITE_GOOGLE_SCRIPT_API_KEY ? '[HIDDEN - KEY SET]' : 'NOT SET',
    allEnvVars: Object.keys(import.meta.env).filter(key => key.startsWith('VITE_'))
  });
  console.log('Environment Variables:', {
    hasGoogleScriptUrl: !!GOOGLE_SCRIPT_URL,
    hasApiKey: !!API_KEY,
    googleScriptUrl: GOOGLE_SCRIPT_URL ? '[HIDDEN - URL LOADED]' : 'NOT SET',
    apiKeyLength: API_KEY?.length || 0,
    origin: window.location.origin
  });

  if (!GOOGLE_SCRIPT_URL) {
    console.error('❌ VITE_GOOGLE_SCRIPT_URL not set in environment variables');
    return;
  }

  if (!API_KEY) {
    console.error('❌ VITE_GOOGLE_SCRIPT_API_KEY not set in environment variables');
    return;
  }

  try {
    const testData = {
      email: '<EMAIL>',
      firstName: 'Test',
      role: 'Test Role',
      useCase: 'Test Use Case',
      consent: true,
      timestamp: new Date().toISOString(),
      apiKey: API_KEY
    };

    console.log('📤 Sending test request...');
    const response = await fetch(GOOGLE_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': window.location.origin,
        'Referer': window.location.href,
      },
      body: JSON.stringify(testData),
      signal: AbortSignal.timeout(10000)
    });

    console.log('📥 Response received:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    const result = await response.json();
    console.log('📋 Response body:', result);

    if (response.ok && result.success) {
      console.log('✅ Google Apps Script connection successful!');
    } else {
      console.error('❌ Google Apps Script returned error:', result);
    }

  } catch (error) {
    console.error('❌ Connection test failed:', error);
  }
};

/**
 * Validate and sanitize input data on the client side
 */
function validateInput(data: Omit<WaitlistSubmission, 'timestamp'>): { valid: boolean; error?: string; sanitizedData?: WaitlistSubmission } {
  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    return { valid: false, error: 'Invalid email format' };
  }

  // Required field validation
  if (!data.firstName?.trim()) {
    return { valid: false, error: 'First name is required' };
  }

  if (!data.role?.trim()) {
    return { valid: false, error: 'Role is required' };
  }

  if (!data.useCase?.trim()) {
    return { valid: false, error: 'Use case is required' };
  }

  if (!data.consent) {
    return { valid: false, error: 'Consent is required' };
  }

  // Sanitize data
  const sanitizedData: WaitlistSubmission = {
    email: data.email.trim().toLowerCase(),
    firstName: data.firstName.trim().substring(0, 50),
    company: data.company?.trim().substring(0, 100) || '',
    role: data.role.trim().substring(0, 50),
    useCase: data.useCase.trim().substring(0, 100),
    dataVolume: data.dataVolume?.trim().substring(0, 50) || '',
    source: data.source?.trim().substring(0, 50) || '',
    consent: Boolean(data.consent),
    timestamp: new Date().toISOString(),
  };

  return { valid: true, sanitizedData };
}

/**
 * Submit waitlist data to Google Sheets via Google Apps Script with security measures
 *
 * @param data - The waitlist form data to submit
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export const submitToGoogleSheets = async (data: WaitlistSubmission): Promise<boolean> => {
  const GOOGLE_SCRIPT_URL = import.meta.env.VITE_GOOGLE_SCRIPT_URL;
  const API_KEY = import.meta.env.VITE_GOOGLE_SCRIPT_API_KEY;

  if (!GOOGLE_SCRIPT_URL) {
    console.warn('Google Script URL not configured in environment variables');
    return false;
  }

  if (!API_KEY) {
    console.warn('Google Script API key not configured in environment variables');
    return false;
  }

  try {
    // Add security headers and API key to the request
    const secureData = {
      ...data,
      apiKey: API_KEY,
    };

    console.log('Submitting to Google Sheets:', {
      url: '[HIDDEN - SUBMITTING TO GOOGLE SCRIPT]',
      dataKeys: Object.keys(secureData),
      hasApiKey: !!API_KEY,
      origin: window.location.origin
    });

    const response = await fetch(GOOGLE_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': window.location.origin,
        'Referer': window.location.href,
      },
      body: JSON.stringify(secureData),
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      // Handle specific HTTP status codes
      if (response.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      } else if (response.status === 403) {
        throw new Error('Access denied. Please contact support.');
      } else if (response.status === 401) {
        throw new Error('Authentication failed.');
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('Raw response:', responseText);

    let result: any;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse response as JSON:', parseError);
      throw new Error(`Invalid response format: ${responseText.substring(0, 200)}`);
    }

    console.log('Google Apps Script response:', {
      status: response.status,
      statusText: response.statusText,
      result: result
    });

    // Check if the Google Apps Script returned a success indicator
    if (result.success === false) {
      console.error('Google Apps Script returned failure:', result.error);
      throw new Error(result.error || 'Google Apps Script returned failure');
    }

    console.log('Successfully submitted to Google Sheets');
    return true;

  } catch (error) {
    console.error('❌ Failed to submit to Google Sheets:', error);

    // Log additional debugging information
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
    }

    // Log environment variables (without sensitive data)
    console.error('Environment check:', {
      hasGoogleScriptUrl: !!GOOGLE_SCRIPT_URL,
      hasApiKey: !!API_KEY,
      googleScriptUrlLength: GOOGLE_SCRIPT_URL?.length || 0,
      apiKeyLength: API_KEY?.length || 0
    });

    // Check if it's a rate limit error specifically
    if (error instanceof Error && error.message.includes('Rate limit')) {
      console.warn('Rate limit exceeded - this is expected behavior for spam protection');
    }

    return false;
  }
};

/**
 * Fallback function to store data locally when Google Sheets is unavailable
 * 
 * @param data - The waitlist form data to store locally
 */
export const storeLocally = (data: WaitlistSubmission): void => {
  try {
    const existingData = localStorage.getItem('detailedWaitlist') || '[]';
    const waitlist = JSON.parse(existingData);
    waitlist.push(data);
    localStorage.setItem('detailedWaitlist', JSON.stringify(waitlist));
    console.log('Data stored locally as fallback');
  } catch (error) {
    console.error('Failed to store data locally:', error);
  }
};

/**
 * Main function to handle waitlist submission with security checks and fallback
 *
 * @param data - The waitlist form data
 * @returns Promise<{ success: boolean, method: 'sheets' | 'local', error?: string }>
 */
export const handleWaitlistSubmission = async (data: Omit<WaitlistSubmission, 'timestamp'>): Promise<{ success: boolean, method: 'sheets' | 'local', error?: string }> => {
  // Security Check 1: Client-side rate limiting
  const rateLimitCheck = checkClientRateLimit();
  if (!rateLimitCheck.allowed) {
    return {
      success: false,
      method: 'local',
      error: `Too many submissions. Please try again in ${rateLimitCheck.timeUntilReset} minutes.`
    };
  }

  // Security Check 2: Input validation and sanitization
  const validation = validateInput(data);
  if (!validation.valid) {
    return {
      success: false,
      method: 'local',
      error: validation.error
    };
  }

  const submissionData = validation.sanitizedData!;

  // Try Google Sheets first
  console.log('Attempting Google Sheets submission...');
  const sheetsSuccess = await submitToGoogleSheets(submissionData);

  if (sheetsSuccess) {
    // Only record successful submissions for rate limiting
    recordSubmission();
    console.log('✅ Successfully submitted to Google Sheets');
    return { success: true, method: 'sheets' };
  }

  // Fallback to local storage
  console.log('⚠️ Google Sheets failed, falling back to local storage');
  storeLocally(submissionData);
  // Also record successful local storage as a submission
  recordSubmission();
  return { success: true, method: 'local' };
};
