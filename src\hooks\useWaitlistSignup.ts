
import { useState } from 'react';
import { toast } from "@/hooks/use-toast";
import type { WaitlistData } from '@/types';
import { handleWaitlistSubmission } from '@/utils/googleSheets';

export const useWaitlistSignup = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleWaitlistSignup = async (email: string): Promise<void> => {
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create minimal waitlist data for simple email signup
      const waitlistData = {
        email,
        firstName: '', // Empty for simple signup
        role: 'Not specified',
        useCase: 'Not specified',
        consent: true, // Implied consent for simple signup
      };

      const result = await handleWaitlistSubmission(waitlistData);

      if (result.success) {
        toast({
          title: "Welcome to the waitlist! 🎉",
          description: "We'll notify you as soon as DataGent is available. Check your email for a confirmation.",
        });
      } else {
        throw new Error('Failed to submit to waitlist');
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      toast({
        title: "Something went wrong",
        description: "Please try again in a moment.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleWaitlistSignup,
    isSubmitting,
  };
};
