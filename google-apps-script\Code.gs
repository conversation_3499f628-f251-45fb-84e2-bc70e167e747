/**
 * Google Apps Script for DataGent Waitlist Integration
 *
 * This script receives POST requests from your React app and writes
 * the waitlist data to a Google Sheet with security measures.
 *
 * Setup Instructions:
 * 1. Create a new Google Sheet
 * 2. Note the Sheet ID from the URL
 * 3. Update the SHEET_ID constant below
 * 4. Deploy this script as a web app
 * 5. Set execution as "Anyone" and access as "Anyone, even anonymous"
 * 6. Copy the web app URL to your .env file as REACT_APP_GOOGLE_SCRIPT_URL
 */

/**
 * Configuration Management
 * All configuration is now stored in PropertiesService and can be set via the setup function
 * This eliminates the need to hardcode sensitive values in the script
 */

function getConfig() {
  const properties = PropertiesService.getScriptProperties();

  return {
    SHEET_ID: properties.getProperty('SHEET_ID'),
    SHEET_NAME: properties.getProperty('SHEET_NAME') || 'Waitlist',
    API_KEY: properties.getProperty('API_KEY'),
    ALLOWED_ORIGINS: JSON.parse(properties.getProperty('ALLOWED_ORIGINS') || '[]'),
    MAX_REQUESTS_PER_MINUTE: parseInt(properties.getProperty('MAX_REQUESTS_PER_MINUTE') || '10'),
    MAX_REQUESTS_PER_HOUR: parseInt(properties.getProperty('MAX_REQUESTS_PER_HOUR') || '100')
  };
}

/**
 * Setup function to configure the Google Apps Script
 * Run this function once after deployment to set your configuration
 * You can also run it again to update configuration
 */
function setupConfiguration() {
  const properties = PropertiesService.getScriptProperties();

  // Default configuration - update these values as needed
  const config = {
    'SHEET_ID': 'YOUR_GOOGLE_SHEET_ID_HERE', // Replace with your actual Sheet ID
    'SHEET_NAME': 'Waitlist',
    'API_KEY': 'YOUR_SECRET_API_KEY_HERE', // Replace with your generated API key
    'ALLOWED_ORIGINS': JSON.stringify([
      'https://yourdomain.com',
      'https://www.yourdomain.com',
      'http://localhost:3000', // Remove in production
      'http://localhost:5173'  // Remove in production
    ]),
    'MAX_REQUESTS_PER_MINUTE': '10',
    'MAX_REQUESTS_PER_HOUR': '100'
  };

  properties.setProperties(config);

  console.log('Configuration updated successfully!');
  console.log('Current configuration:', getConfig());

  return {
    success: true,
    message: 'Configuration updated successfully',
    config: getConfig()
  };
}

/**
 * Main function that handles POST requests with security checks
 */
function doPost(e) {
  try {
    // Load configuration
    const config = getConfig();

    // Check if configuration is set up
    if (!config.SHEET_ID || !config.API_KEY) {
      console.error('Configuration not set up. Run setupConfiguration() first.');
      return createErrorResponse('Service not configured', 503);
    }

    // Security Check 1: Rate limiting
    const rateLimitResult = checkRateLimit(config);
    if (!rateLimitResult.allowed) {
      return createErrorResponse('Rate limit exceeded. Please try again later.', 429);
    }

    // Security Check 2: Origin validation
    const origin = e.parameter.origin || getOriginFromHeaders(e);
    if (!isAllowedOrigin(origin, config)) {
      console.warn('Blocked request from unauthorized origin:', origin);
      return createErrorResponse('Unauthorized origin', 403);
    }

    // Security Check 3: API Key validation
    const providedKey = e.parameter.apiKey || e.postData?.contents && JSON.parse(e.postData.contents).apiKey;
    if (!isValidApiKey(providedKey, config)) {
      console.warn('Invalid or missing API key');
      return createErrorResponse('Invalid authentication', 401);
    }

    // Parse the JSON data from the request
    const data = JSON.parse(e.postData.contents);

    // Security Check 4: Input validation and sanitization
    const validationResult = validateAndSanitizeInput(data);
    if (!validationResult.valid) {
      return createErrorResponse(validationResult.error, 400);
    }

    // Security Check 5: Check for duplicate submissions
    const duplicateCheck = checkForDuplicate(validationResult.data.email, config);
    if (duplicateCheck.isDuplicate) {
      console.log('Duplicate submission detected for email:', validationResult.data.email);
      // Return success to prevent information leakage, but don't add to sheet
      return createSuccessResponse('Data processed successfully', null);
    }

    // Write to Google Sheet
    const result = writeToSheet(validationResult.data, config);

    if (result.success) {
      // Log successful submission (without sensitive data)
      console.log('Successful submission from origin:', origin, 'at', new Date().toISOString());
      return createSuccessResponse('Data successfully added to waitlist', result.rowNumber);
    } else {
      return createErrorResponse(result.error, 500);
    }

  } catch (error) {
    console.error('Error in doPost:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Handle GET requests (disabled for security)
 */
function doGet(e) {
  // Disable GET requests for security
  return createErrorResponse('Method not allowed', 405);
}

/**
 * Security Functions
 */

// Rate limiting using PropertiesService
function checkRateLimit(config) {
  const now = new Date();
  const currentMinute = Math.floor(now.getTime() / 60000);
  const currentHour = Math.floor(now.getTime() / 3600000);

  const properties = PropertiesService.getScriptProperties();

  // Check minute-based rate limit
  const minuteKey = `rate_limit_minute_${currentMinute}`;
  const minuteCount = parseInt(properties.getProperty(minuteKey) || '0');

  if (minuteCount >= config.MAX_REQUESTS_PER_MINUTE) {
    return { allowed: false, reason: 'Too many requests per minute' };
  }

  // Check hour-based rate limit
  const hourKey = `rate_limit_hour_${currentHour}`;
  const hourCount = parseInt(properties.getProperty(hourKey) || '0');

  if (hourCount >= config.MAX_REQUESTS_PER_HOUR) {
    return { allowed: false, reason: 'Too many requests per hour' };
  }

  // Increment counters
  properties.setProperty(minuteKey, (minuteCount + 1).toString());
  properties.setProperty(hourKey, (hourCount + 1).toString());

  // Clean up old rate limit entries (keep only current and previous periods)
  cleanupRateLimitData(currentMinute, currentHour);

  return { allowed: true };
}

function cleanupRateLimitData(currentMinute, currentHour) {
  const properties = PropertiesService.getScriptProperties();
  const allProperties = properties.getProperties();

  Object.keys(allProperties).forEach(key => {
    if (key.startsWith('rate_limit_minute_')) {
      const minute = parseInt(key.split('_')[3]);
      if (minute < currentMinute - 1) {
        properties.deleteProperty(key);
      }
    } else if (key.startsWith('rate_limit_hour_')) {
      const hour = parseInt(key.split('_')[3]);
      if (hour < currentHour - 1) {
        properties.deleteProperty(key);
      }
    }
  });
}

function isAllowedOrigin(origin, config) {
  if (!origin) return false;
  return config.ALLOWED_ORIGINS.some(allowed =>
    origin === allowed ||
    origin.endsWith('.' + allowed.replace(/^https?:\/\//, ''))
  );
}

function getOriginFromHeaders(e) {
  // Try to extract origin from headers
  const headers = e.parameter;
  return headers.origin || headers.referer || null;
}

function isValidApiKey(providedKey, config) {
  return providedKey === config.API_KEY;
}

function validateAndSanitizeInput(data) {
  // Check required fields
  if (!data.email || !data.firstName) {
    return {
      valid: false,
      error: 'Missing required fields: email and firstName'
    };
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    return {
      valid: false,
      error: 'Invalid email format'
    };
  }

  // Sanitize and validate string lengths
  const sanitizedData = {
    email: data.email.trim().toLowerCase().substring(0, 254), // RFC 5321 limit
    firstName: sanitizeString(data.firstName, 50),
    company: sanitizeString(data.company || '', 100),
    role: sanitizeString(data.role || '', 50),
    useCase: sanitizeString(data.useCase || '', 100),
    dataVolume: sanitizeString(data.dataVolume || '', 50),
    source: sanitizeString(data.source || '', 50),
    consent: Boolean(data.consent),
    timestamp: data.timestamp || new Date().toISOString()
  };

  // Additional validation
  if (sanitizedData.firstName.length < 1) {
    return {
      valid: false,
      error: 'First name is required'
    };
  }

  return {
    valid: true,
    data: sanitizedData
  };
}

function sanitizeString(str, maxLength) {
  if (typeof str !== 'string') return '';

  // Remove potentially dangerous characters and limit length
  return str
    .trim()
    .replace(/[<>\"'&]/g, '') // Remove HTML/script injection characters
    .substring(0, maxLength);
}

function checkForDuplicate(email, config) {
  try {
    const spreadsheet = SpreadsheetApp.openById(config.SHEET_ID);
    const sheet = spreadsheet.getSheetByName(config.SHEET_NAME);

    if (!sheet) return { isDuplicate: false };

    const emailColumn = 2; // Email is in column B (index 2)
    const lastRow = sheet.getLastRow();

    if (lastRow <= 1) return { isDuplicate: false }; // Only headers or empty

    const emailRange = sheet.getRange(2, emailColumn, lastRow - 1, 1);
    const emails = emailRange.getValues().flat();

    return {
      isDuplicate: emails.includes(email.toLowerCase())
    };

  } catch (error) {
    console.error('Error checking for duplicates:', error);
    return { isDuplicate: false }; // Allow submission if check fails
  }
}

function createSuccessResponse(message, rowNumber) {
  const response = {
    success: true,
    message: message
  };

  // Only include row number for debugging, not in production
  if (rowNumber && typeof rowNumber === 'number') {
    response.rowNumber = rowNumber;
  }

  return ContentService
    .createTextOutput(JSON.stringify(response))
    .setMimeType(ContentService.MimeType.JSON)
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
}

function createErrorResponse(error, statusCode = 400) {
  const response = {
    success: false,
    error: error
  };

  return ContentService
    .createTextOutput(JSON.stringify(response))
    .setMimeType(ContentService.MimeType.JSON)
    .setHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type'
    });
}

/**
 * Write waitlist data to Google Sheet
 */
function writeToSheet(data, config) {
  try {
    // Open the spreadsheet
    const spreadsheet = SpreadsheetApp.openById(config.SHEET_ID);
    let sheet = spreadsheet.getSheetByName(config.SHEET_NAME);
    
    // Create the sheet if it doesn't exist
    if (!sheet) {
      sheet = spreadsheet.insertSheet(config.SHEET_NAME);
      
      // Add headers
      const headers = [
        'Timestamp',
        'Email',
        'First Name',
        'Company',
        'Role',
        'Use Case',
        'Data Volume',
        'Source',
        'Consent',
        'Submission Date'
      ];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
      
      // Format headers
      const headerRange = sheet.getRange(1, 1, 1, headers.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#4285f4');
      headerRange.setFontColor('white');
    }
    
    // Prepare the row data
    const rowData = [
      data.timestamp || new Date().toISOString(),
      data.email || '',
      data.firstName || '',
      data.company || '',
      data.role || '',
      data.useCase || '',
      data.dataVolume || '',
      data.source || '',
      data.consent ? 'Yes' : 'No',
      new Date().toISOString()
    ];
    
    // Add the data to the next available row
    const lastRow = sheet.getLastRow();
    const newRow = lastRow + 1;
    sheet.getRange(newRow, 1, 1, rowData.length).setValues([rowData]);
    
    // Auto-resize columns for better readability
    sheet.autoResizeColumns(1, rowData.length);
    
    console.log('Successfully added data to row:', newRow);
    
    return {
      success: true,
      rowNumber: newRow
    };
    
  } catch (error) {
    console.error('Error writing to sheet:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Configuration management functions
 */

/**
 * Update configuration from environment-like object
 * This allows you to update configuration programmatically
 */
function updateConfiguration(newConfig) {
  const properties = PropertiesService.getScriptProperties();

  const validKeys = ['SHEET_ID', 'SHEET_NAME', 'API_KEY', 'ALLOWED_ORIGINS', 'MAX_REQUESTS_PER_MINUTE', 'MAX_REQUESTS_PER_HOUR'];
  const updates = {};

  Object.keys(newConfig).forEach(key => {
    if (validKeys.includes(key)) {
      if (key === 'ALLOWED_ORIGINS' && Array.isArray(newConfig[key])) {
        updates[key] = JSON.stringify(newConfig[key]);
      } else {
        updates[key] = String(newConfig[key]);
      }
    }
  });

  properties.setProperties(updates);

  console.log('Configuration updated:', updates);
  return {
    success: true,
    message: 'Configuration updated successfully',
    config: getConfig()
  };
}

/**
 * Get current configuration (for debugging)
 */
function getCurrentConfiguration() {
  return getConfig();
}

/**
 * Clear all configuration (use with caution)
 */
function clearConfiguration() {
  const properties = PropertiesService.getScriptProperties();
  const validKeys = ['SHEET_ID', 'SHEET_NAME', 'API_KEY', 'ALLOWED_ORIGINS', 'MAX_REQUESTS_PER_MINUTE', 'MAX_REQUESTS_PER_HOUR'];

  validKeys.forEach(key => {
    properties.deleteProperty(key);
  });

  console.log('Configuration cleared');
  return { success: true, message: 'Configuration cleared' };
}

/**
 * Test function to verify the script works
 * Run this function manually to test
 */
function testScript() {
  const config = getConfig();

  if (!config.SHEET_ID || !config.API_KEY) {
    console.error('Configuration not set up. Run setupConfiguration() first.');
    return { success: false, error: 'Configuration not set up' };
  }

  const testData = {
    email: '<EMAIL>',
    firstName: 'Test',
    company: 'Test Company',
    role: 'Founder / CXO',
    useCase: 'Sales & revenue analytics',
    dataVolume: '1–50 GB',
    source: 'website',
    consent: true,
    timestamp: new Date().toISOString()
  };

  const result = writeToSheet(testData, config);
  console.log('Test result:', result);

  return result;
}
