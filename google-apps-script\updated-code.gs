/**
 * =================================================================
 * S E T U P  &  C O N F I G U R A T I O N
 * =================================================================
 * Run the setup function once to configure the script.
 */

/**
 * Main setup function.
 * REPLACE the placeholder values below with your actual configuration.
 * Then, run this function once from the Apps Script editor.
 */
function setup() {
  const properties = PropertiesService.getScriptProperties();

  // --- CONFIGURE YOUR SCRIPT HERE ---
  const config = {
    // IMPORTANT: Replace with the 44-character ID from your Google Sheet's URL
    'SHEET_ID': '1LsMjPxy-WSTJOJpeBY_90LMEAbkpyi2ETEnoRdhVTwo', 
    
    'SHEET_NAME': 'Waitlist', // The name of the tab in your sheet
    
    // IMPORTANT: Create a secure, random string for your API key
    'API_KEY': '31C+khq5M16kOgVH1jRJpY7H+bnQvmlWURc6SRyazy1yTl3/k+i6c52jskxUOrQm', 
    
    // A list of website URLs that are allowed to submit data
    'ALLOWED_ORIGINS': JSON.stringify([
      "https://datagent.co.ke",
      "https://www.datagent.co.ke",
      "http://localhost:3000",
      "http://localhost:5173",
      "http://localhost:8080"  // For local testing, remove in production
    ]),
    
    'MAX_REQUESTS_PER_MINUTE': '30', // Max requests per minute
    'MAX_REQUESTS_PER_HOUR': '200'   // Max requests per hour
  };
  // ------------------------------------

  // Clear any old, corrupted properties before setting new ones
  properties.deleteAllProperties(); 
  properties.setProperties(config);

  console.log('✅ Configuration has been set up successfully!');
  console.log('Current configuration:', getConfig());
}

/**
 * Retrieves and parses the script configuration.
 */
function getConfig() {
  const properties = PropertiesService.getScriptProperties();
  try {
    return {
      SHEET_ID: properties.getProperty('SHEET_ID'),
      SHEET_NAME: properties.getProperty('SHEET_NAME') || 'Waitlist',
      API_KEY: properties.getProperty('API_KEY'),
      ALLOWED_ORIGINS: JSON.parse(properties.getProperty('ALLOWED_ORIGINS') || '[]'),
      MAX_REQUESTS_PER_MINUTE: parseInt(properties.getProperty('MAX_REQUESTS_PER_MINUTE') || '30'),
      MAX_REQUESTS_PER_HOUR: parseInt(properties.getProperty('MAX_REQUESTS_PER_HOUR') || '200')
    };
  } catch (e) {
    console.error('Failed to parse ALLOWED_ORIGINS. It might be stored incorrectly. Please run the setup() function again.', e);
    // Return a default empty array to prevent further errors
    return { ALLOWED_ORIGINS: [] };
  }
}

/**
 * =================================================================
 * C O R E  A P P L I C A T I O N   L O G I C
 * =================================================================
 * Handles web requests.
 */

/**
 * Main function that handles POST requests.
 */
function doPost(e) {
  const config = getConfig();

  // Enhanced logging for debugging
  console.log('=== REQUEST DEBUG INFO ===');
  console.log('Headers:', e.headers || 'No headers');
  console.log('Parameters:', e.parameter || 'No parameters');
  console.log('Post data:', e.postData ? e.postData.contents : 'No post data');
  console.log('Config loaded:', !!config.SHEET_ID);

  // Check if setup has been run
  if (!config.SHEET_ID || !config.API_KEY || config.SHEET_ID === 'REPLACE_WITH_YOUR_VALID_GOOGLE_SHEET_ID') {
    console.error('🔴 Configuration is incomplete. Please run the setup() function first.');
    return createJsonResponse({ success: false, error: 'Service is not configured.' }, 503);
  }

  try {
    // --- Security Checks ---
    if (!checkRateLimit(config).allowed) {
      console.log('Rate limit exceeded');
      return createJsonResponse({ success: false, error: 'Rate limit exceeded.' }, 429);
    }
    
    // Enhanced origin checking with multiple fallbacks
    const origin = getOriginFromRequest(e);
    console.log('Detected origin:', origin);
    console.log('Allowed origins:', config.ALLOWED_ORIGINS);
    
    if (!isAllowedOrigin(origin, config)) {
      console.log('Origin not allowed:', origin);
      return createJsonResponse({ success: false, error: 'Unauthorized origin.' }, 403);
    }
    
    // Parse and validate post data
    if (!e.postData || !e.postData.contents) {
      console.error('No post data received');
      return createJsonResponse({ success: false, error: 'No data received.' }, 400);
    }
    
    const postData = JSON.parse(e.postData.contents);
    console.log('Parsed data keys:', Object.keys(postData));
    
    if (!isValidApiKey(postData.apiKey, config)) {
      console.log('Invalid API key provided');
      return createJsonResponse({ success: false, error: 'Invalid authentication.' }, 401);
    }
    
    // --- Input Validation & Processing ---
    const validationResult = validateAndSanitizeInput(postData);
    if (!validationResult.valid) {
      console.log('Validation failed:', validationResult.error);
      return createJsonResponse({ success: false, error: validationResult.error }, 400);
    }
    
    if (checkForDuplicate(validationResult.data.email, config)) {
      console.log('Duplicate email detected:', validationResult.data.email);
      // Return a success message to prevent leaking information about existing emails
      return createJsonResponse({ success: true, message: 'Your submission has been received.' });
    }
    
    // --- Write to Sheet ---
    console.log('Writing to sheet...');
    const writeResult = writeToSheet(validationResult.data, config);
    if (writeResult.success) {
      console.log('✅ Successfully wrote to sheet');
      return createJsonResponse({ success: true, message: 'Successfully joined the waitlist!' });
    } else {
      console.error('Failed to write to sheet:', writeResult.error);
      throw new Error(writeResult.error);
    }

  } catch (error) {
    console.error('Error in doPost:', error);
    return createJsonResponse({ success: false, error: 'An internal server error occurred.' }, 500);
  }
}

/**
 * Enhanced origin detection with multiple fallbacks
 */
function getOriginFromRequest(e) {
  // Try multiple ways to get the origin
  if (e.headers) {
    return e.headers.origin || e.headers.Origin || e.headers.referer || e.headers.Referer;
  }
  
  if (e.parameter) {
    return e.parameter.origin;
  }
  
  return null;
}

/**
 * Handles GET requests (disabled for security).
 */
function doGet(e) {
  return createJsonResponse({ success: false, error: 'Method not allowed.' }, 405);
}

/**
 * =================================================================
 * H E L P E R  &  S E C U R I T Y   F U N C T I O N S
 * =================================================================
 */
 
/**
 * Writes the sanitized data to the Google Sheet.
 */
function writeToSheet(data, config) {
  try {
    const spreadsheet = SpreadsheetApp.openById(config.SHEET_ID);
    let sheet = spreadsheet.getSheetByName(config.SHEET_NAME);

    if (!sheet) {
      sheet = spreadsheet.insertSheet(config.SHEET_NAME);
      const headers = ['Timestamp', 'Email', 'First Name', 'Company', 'Role', 'Use Case', 'Data Volume', 'Source', 'Consent'];
      sheet.appendRow(headers);
      sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
      sheet.setFrozenRows(1);
    }

    const newRow = [
      new Date(),
      data.email,
      data.firstName,
      data.company,
      data.role,
      data.useCase,
      data.dataVolume,
      data.source,
      data.consent
    ];
    sheet.appendRow(newRow);
    
    return { success: true };
  } catch (error) {
    console.error('Error writing to sheet:', error);
    // Check for common permission/ID errors
    if (error.message.includes("You do not have permission") || error.message.includes("not found")) {
        return { success: false, error: "Failed to access the Google Sheet. Please check that the SHEET_ID is correct and that you have granted the script permission." };
    }
    return { success: false, error: error.toString() };
  }
}

/**
 * Validates and sanitizes user input data.
 */
function validateAndSanitizeInput(data) {
  if (!data || !data.email || !data.firstName) {
    return { valid: false, error: 'Missing required fields: email and firstName.' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    return { valid: false, error: 'Invalid email format.' };
  }

  const sanitize = (str, maxLength) => (str || '').toString().trim().substring(0, maxLength);

  return {
    valid: true,
    data: {
      email: sanitize(data.email, 254).toLowerCase(),
      firstName: sanitize(data.firstName, 50),
      company: sanitize(data.company, 100),
      role: sanitize(data.role, 50),
      useCase: sanitize(data.useCase, 100),
      dataVolume: sanitize(data.dataVolume, 50),
      source: sanitize(data.source, 50),
      consent: data.consent === true,
    }
  };
}

/**
 * Checks if an email already exists in the sheet.
 */
function checkForDuplicate(email, config) {
  try {
    const sheet = SpreadsheetApp.openById(config.SHEET_ID).getSheetByName(config.SHEET_NAME);
    if (!sheet || sheet.getLastRow() < 2) return false;
    const emailColumn = 2; // Column B
    const emails = sheet.getRange(2, emailColumn, sheet.getLastRow() - 1, 1).getValues().flat();
    return emails.includes(email);
  } catch(e) {
      console.error("Could not check for duplicates. Allowing submission.", e);
      return false; // Fail open
  }
}

function isAllowedOrigin(origin, config) {
  if (!origin) return false;
  
  // Extract just the origin part from full URLs
  try {
    const url = new URL(origin);
    const cleanOrigin = `${url.protocol}//${url.host}`;
    return config.ALLOWED_ORIGINS.includes(cleanOrigin);
  } catch (e) {
    // If URL parsing fails, try direct comparison
    return config.ALLOWED_ORIGINS.includes(origin);
  }
}

function isValidApiKey(providedKey, config) {
  return providedKey && providedKey === config.API_KEY;
}

function checkRateLimit(config) {
  // This is a simplified rate-limiting stub. For production, consider a more robust implementation.
  const cache = CacheService.getScriptCache();
  const key = 'rate_limit_' + new Date().getHours();
  const count = parseInt(cache.get(key) || '0');
  
  if (count >= config.MAX_REQUESTS_PER_HOUR) {
    return { allowed: false };
  }
  
  cache.put(key, (count + 1).toString(), 3600); // Expires in 1 hour
  return { allowed: true };
}

/**
 * Creates a JSON response for the web app.
 */
function createJsonResponse(data, statusCode = 200) {
  const response = ContentService.createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
    
  // Add CORS headers to allow requests from your website
  response.setHeader('Access-Control-Allow-Origin', '*'); 
  response.setHeader('Access-Control-Allow-Methods', 'POST');
  response.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  return response;
}

/**
 * =================================================================
 * T E S T I N G
 * =================================================================
 */

/**
 * Test function to verify the core logic works after setup.
 * Run this function manually from the editor.
 */
function testScript() {
  console.log("Running test script...");
  const config = getConfig();

  if (!config.SHEET_ID || config.SHEET_ID === 'REPLACE_WITH_YOUR_VALID_GOOGLE_SHEET_ID') {
    console.error('🔴 Test Failed: Configuration is not set up. Please run setup() first.');
    return { success: false, error: 'Configuration not set up.' };
  }

  const testData = {
    email: `test-${new Date().getTime()}@example.com`, // Unique email for testing
    firstName: 'Test User',
    company: 'TestCo',
    role: 'Tester',
    useCase: 'Script validation',
    dataVolume: '1-5 GB',
    source: 'testScript',
    consent: true
  };

  const result = writeToSheet(testData, config);

  if (result.success) {
    console.log('✅ Test Passed: Successfully wrote a test row to the sheet.');
  } else {
    console.error('🔴 Test Failed:', result.error);
  }

  return result;
}
