import React from 'react';
import { useWaitlistSignup } from "@/hooks/useWaitlistSignup";

// Import all modular components
import Header from "@/components/sections/Header";
import HeroSection from "@/components/sections/HeroSection";
import ProblemSection from "@/components/sections/ProblemSection";
import ExpertComparisonSection from "@/components/sections/ExpertComparisonSection";

//import SuccessStorySection from "@/components/sections/SuccessStorySection";
import FinalCTASection from "@/components/sections/FinalCTASection";
import Footer from "@/components/sections/Footer";
import { DebugPanel } from "@/components/DebugPanel";

// Keep existing sections that weren't refactored
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  BarChart3, 
  Target, 
  Zap, 
  TrendingUp,
  Star,
  Settings,
  CheckCircle
} from "lucide-react";

const Index = () => {
  const { handleWaitlistSignup } = useWaitlistSignup();

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <HeroSection />
      <ProblemSection />
      <ExpertComparisonSection />
      
      
      {/* How It Works Section - Enhanced */}
      <section id="how-it-works" className="px-4 sm:px-6 lg:px-8 py-24 bg-muted/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl lg:text-6xl font-bold text-foreground mb-8">
              How DataGent Works
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Get started with AI that understands your business in three simple steps. 
              No technical knowledge required.
            </p>
          </div>
          
          <div className="space-y-24">
            {/* Step 1 */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="relative order-2 lg:order-1">
                <div className="aspect-[16/10] bg-gradient-to-br from-purple-100 to-blue-100 rounded-3xl overflow-hidden relative shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <iframe 
                  src="https://www.youtube.com/embed/5gejbzqlGeQ?autoplay=1&mute=1&loop=1&playlist=5gejbzqlGeQ" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover">
                  </iframe>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mr-6 shadow-xl">
                    1
                  </div>
                  <h3 className="text-4xl font-bold text-foreground">Build Your Business Brain</h3>
                </div>
                <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                  Add a data source:  Excel, Docx, PDF, SQL dumps, APIs, live streams, website, pitch deck, or SOPs.
                  Our Business Profile Builder turns them into a living knowledge graph that teaches every AI persona 
                  your KPIs, glossaries, and goals.
                </p>
                <div className="bg-green-50 border border-green-200 rounded-2xl p-6">
                  <p className="text-green-700 font-medium">
                    "I want to understand which products sell best and when to run promotions"
                  </p>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mr-6 shadow-xl">
                    2
                  </div>
                  <h3 className="text-4xl font-bold text-foreground">Get Your AI Agent</h3>
                </div>
                <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                  We instantly match you with a specialized AI agent trained for your specific role — 
                  data analyst, marketing strategist, business consultant, or automation expert. 
                  Each agent comes pre-loaded with industry knowledge.
                  All personas inherit your business context, so answers are never generic.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-2xl p-6">
                  <p className="text-blue-700 font-medium">
                    ✨ Your agent is ready to work and understands your business context
                  </p>
                </div>
              </div>
              <div className="relative">
                <div className="aspect-[16/10] bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl overflow-hidden relative shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <iframe 
                  src="https://youtube.com/embed/0UdjhrT1_pk?autoplay=1&mute=1&loop=1&playlist=0UdjhrT1_pk" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover">
                  </iframe>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="relative order-2 lg:order-1">
                <div className="aspect-[16/10] bg-gradient-to-br from-purple-100 to-blue-100 rounded-3xl overflow-hidden relative shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <iframe 
                  src="https://www.youtube.com/embed/DBMZkm1oUMM?autoplay=1&mute=1&loop=1&playlist=DBMZkm1oUMM" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover">
                  </iframe>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mr-6 shadow-xl">
                    3
                  </div>
                  <h3 className="text-4xl font-bold text-foreground">Watch Your Business Grow</h3>
                </div>
                <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                  Add new personas from the marketplace anytime; the Concierge instantly adds them to the team.
                  Your AI agents work continuously and collaboratively to analyze data, generate insights, create content, 
                  and automate tasks. Get daily reports, actionable recommendations, and watch your key metrics improve week 
                  after week.
                </p>
                <div className="bg-purple-50 border border-purple-200 rounded-2xl p-6">
                  <p className="text-purple-700 font-medium">
                    🚀 Most users see measurable results within the first 30 days
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section - Enhanced with Better Balance */}
      <section className="px-4 sm:px-6 lg:px-8 py-24 bg-muted/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl lg:text-6xl font-bold text-foreground mb-8">
              What Can You Do with DataGent?
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Discover the endless possibilities with AI agents designed for your specific business needs.
            </p>
          </div>
          
          {/* Balanced 2x2 Grid Layout */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Row 1 - Data Analysis & Marketing Content */}
            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group border-0 shadow-xl">
              <div className="aspect-video bg-gradient-to-br from-purple-100 to-blue-100 relative overflow-hidden">
                <iframe 
                  src="https://youtube.com/embed/0UdjhrT1_pk?autoplay=1&mute=1&loop=1&playlist=0UdjhrT1_pk" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover"
                ></iframe>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <div className="flex items-center space-x-3 mb-3">
                    <BarChart3 className="w-6 h-6" />
                    <span className="text-xl font-bold">📊 Data Analysis</span>
                  </div>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Transform your spreadsheets into clear insights. Ask questions about your data 
                    and get beautiful charts and actionable recommendations instantly.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group border-0 shadow-xl">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
                <iframe 
                  src="https://www.youtube.com/embed/tBTM1Ri3KuY?autoplay=1&mute=1&loop=1&playlist=tBTM1Ri3KuY" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover"
                ></iframe>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <div className="flex items-center space-x-3 mb-3">
                    <Target className="w-6 h-6" />
                    <span className="text-xl font-bold">📣 Marketing Content</span>
                  </div>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Generate high-performing social media posts, email campaigns, and marketing strategies 
                    tailored to your audience and brand voice.
                  </p>
                </div>
              </div>
            </Card>

            {/* Row 2 - Business Strategy & Custom AI Agents */}
            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group border-0 shadow-xl">
              <div className="aspect-video bg-gradient-to-br from-purple-100 to-blue-100 relative overflow-hidden">
                <iframe 
                  src="https://www.youtube.com/embed/DBMZkm1oUMM?autoplay=1&mute=1&loop=1&playlist=DBMZkm1oUMM" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover"
                ></iframe>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <div className="flex items-center space-x-3 mb-3">
                    <TrendingUp className="w-6 h-6" />
                    <span className="text-xl font-bold">📈 Business Strategy</span>
                  </div>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Get expert guidance on business planning, growth projections, competitive analysis, 
                    and strategic decisions that drive results.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 group border-0 shadow-xl">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
                <iframe 
                  src="https://www.youtube.com/embed/5gejbzqlGeQ?autoplay=1&mute=1&loop=1&playlist=5gejbzqlGeQ" 
                  title="Data Analysis in action" 
                  allow="autoplay;"
                  className="w-full h-full object-cover"
                ></iframe>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <div className="flex items-center space-x-3 mb-3">
                    <Settings className="w-6 h-6" />
                    <span className="text-xl font-bold">🤖 Build your business profile</span>
                  </div>
                  <p className="text-sm opacity-90 leading-relaxed">
                    Use specialized agents that understand your business and deliver results from day one. 
                    Or build your own custom AI agent using our plug-and-play modules. Combine data analysis, 
                    automation, and content creation for your unique workflow.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/*<SuccessStorySection />*/}

      {/* Testimonials Section - Enhanced */}
      <section id="testimonials" className="px-4 sm:px-6 lg:px-8 py-24">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold text-foreground mb-8">
              Loved by businesses across Africa
            </h2>
            <p className="text-2xl text-muted-foreground">
              Join the 500+ businesses already transforming their operations with DataGent
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-10">
            <Card className="p-10 hover:shadow-2xl transition-all duration-500 border-0 shadow-xl">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-muted-foreground mb-8 text-lg leading-relaxed">
                "Finally, an AI tool that understands my restaurant business. Sales predictions 
                are 95% accurate and marketing campaigns actually bring in customers. Revenue up 35% in 2 months."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  M
                </div>
                <div className="ml-4">
                  <p className="font-bold text-foreground text-lg">Mike Ochieng</p>
                  <p className="text-muted-foreground">Savannah Grill, Mombasa</p>
                </div>
              </div>
            </Card>
            
            <Card className="p-10 hover:shadow-2xl transition-all duration-500 border-0 shadow-xl">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-muted-foreground mb-8 text-lg leading-relaxed">
                "As a consultant, I was skeptical about AI. But DataGent helps me deliver better 
                insights to my clients in half the time. It's become essential to my practice."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  S
                </div>
                <div className="ml-4">
                  <p className="font-bold text-foreground text-lg">Sarah Kimani</p>
                  <p className="text-muted-foreground">Business Consultant, Kigali</p>
                </div>
              </div>
            </Card>
            
            <Card className="p-10 hover:shadow-2xl transition-all duration-500 border-0 shadow-xl">
              <div className="flex items-center mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-muted-foreground mb-8 text-lg leading-relaxed">
                "DataGent transformed our e-commerce operations. Inventory optimization alone 
                saved us $10K in the first month. The ROI is incredible — 300% in 90 days."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  A
                </div>
                <div className="ml-4">
                  <p className="font-bold text-foreground text-lg">Alex Ndungu</p>
                  <p className="text-muted-foreground">TechMart Online, Lagos</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <div id="waitlist">
        <FinalCTASection />
      </div>
      <Footer />

      {/* Debug Panel - only visible in development */}
      {process.env.NODE_ENV === 'development' && <DebugPanel />}
    </div>
  );
};

export default Index;
