import React from 'react';
import { Button } from "@/components/ui/button";
import { debugRateLimit, clearRateLimit } from '@/utils/googleSheets';

interface DebugPanelProps {
  isVisible?: boolean;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({ isVisible = false }) => {
  const [showDebug, setShowDebug] = React.useState(isVisible);

  const handleClearRateLimit = () => {
    clearRateLimit();
    debugRateLimit();
  };

  const handleCheckRateLimit = () => {
    debugRateLimit();
  };

  const handleClearAllData = () => {
    localStorage.removeItem('waitlist_submissions');
    localStorage.removeItem('detailedWaitlist');
    console.log('All waitlist data cleared');
  };

  const handleShowLocalData = () => {
    const localData = localStorage.getItem('detailedWaitlist');
    if (localData) {
      console.log('Local waitlist data:', JSON.parse(localData));
    } else {
      console.log('No local waitlist data found');
    }
  };

  if (!showDebug) {
    return (
      <div className="fixed bottom-4 right-4">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setShowDebug(true)}
        >
          Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-sm">Debug Panel</h3>
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => setShowDebug(false)}
        >
          ×
        </Button>
      </div>
      
      <div className="space-y-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full text-xs"
          onClick={handleCheckRateLimit}
        >
          Check Rate Limit
        </Button>
        
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full text-xs"
          onClick={handleClearRateLimit}
        >
          Clear Rate Limit
        </Button>
        
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full text-xs"
          onClick={handleShowLocalData}
        >
          Show Local Data
        </Button>
        
        <Button 
          variant="destructive" 
          size="sm" 
          className="w-full text-xs"
          onClick={handleClearAllData}
        >
          Clear All Data
        </Button>
      </div>
      
      <div className="mt-3 text-xs text-gray-500">
        Check browser console for debug output
      </div>
    </div>
  );
};
