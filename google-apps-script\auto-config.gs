/**
 * Auto-generated configuration for Google Apps Script
 * Generated on: 2025-07-30T16:25:32.054Z
 * 
 * Copy and paste this code into your Google Apps Script editor,
 * then run the updateConfiguration() function.
 */

function autoSetupConfiguration() {
  const config = {
  "SHEET_ID": "1LsMjPxy-WSTJOJpeBY_90LMEAbkpyi2ETEnoRdhVTwo",
  "SHEET_NAME": "Genius Waitlist",
  "API_KEY": "31C+khq5M16kOgVH1jRJpY7H+bnQvmlWURc6SRyazy1yTl3/k+i6c52jskxUOrQm",
  "ALLOWED_ORIGINS": [
    "https://datagent.co.ke",
    "https://www.datagent.co.ke",
    "http://localhost:3000",
    "http://localhost:5173",
    "http://localhost:8080"
  ],
  "MAX_REQUESTS_PER_MINUTE": "10",
  "MAX_REQUESTS_PER_HOUR": "100"
};
  
  return updateConfiguration(config);
}

// Run this function to apply the configuration
function applyAutoConfiguration() {
  console.log('Applying auto-generated configuration...');
  const result = autoSetupConfiguration();
  console.log('Configuration result:', result);
  return result;
}