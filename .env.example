# Google Sheets Integration Configuration
# All configuration is now managed through environment variables

# Required: Google Apps Script web app URL
# Get this after deploying your Google Apps Script as a web app
# Example: https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
REACT_APP_GOOGLE_SCRIPT_URL=

# Required: Google Sheet ID (from the Google Sheets URL)
# Example: 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
REACT_APP_GOOGLE_SHEET_ID=

# Required: API key for authentication (generate using npm run generate-api-key)
# This must match the key configured in your Google Apps Script
REACT_APP_GOOGLE_SCRIPT_API_KEY=

# Optional: Google Sheet tab name (defaults to "Waitlist")
REACT_APP_GOOGLE_SHEET_NAME=Waitlist

# Optional: Your production domain (used for CORS validation)
# Example: yourdomain.com (without https://)
REACT_APP_PRODUCTION_DOMAIN=

# Optional: Additional allowed origins (comma-separated)
# Example: https://staging.yourdomain.com,https://preview.yourdomain.com
REACT_APP_ALLOWED_ORIGINS=

# Optional: Rate limiting configuration
REACT_APP_MAX_REQUESTS_PER_MINUTE=10
REACT_APP_MAX_REQUESTS_PER_HOUR=100

# Optional: Environment (affects which origins are allowed)
NODE_ENV=development

# Optional: Other configuration
# REACT_APP_API_BASE_URL=
# REACT_APP_ANALYTICS_ID=
