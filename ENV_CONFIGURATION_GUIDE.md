# Environment-Based Configuration Guide

## 🎯 Overview

All configuration for your Google Sheets integration is now managed through environment variables in your `.env` file. No more hardcoding values in Google Apps Script!

## 🔧 Quick Setup

### 1. Generate Configuration
```bash
# Generate API key
npm run generate-api-key

# Copy environment template
cp .env.example .env

# Generate Google Apps Script configuration
npm run setup-google-script
```

### 2. Configure Environment Variables
Edit your `.env` file with the generated values:

```bash
# Required Configuration
REACT_APP_GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
REACT_APP_GOOGLE_SHEET_ID=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
REACT_APP_GOOGLE_SCRIPT_API_KEY=your-generated-api-key-here

# Domain Configuration
REACT_APP_PRODUCTION_DOMAIN=yourdomain.com
NODE_ENV=production

# Optional Configuration
REACT_APP_GOOGLE_SHEET_NAME=Waitlist
REACT_APP_MAX_REQUESTS_PER_MINUTE=10
REACT_APP_MAX_REQUESTS_PER_HOUR=100
```

### 3. Apply to Google Apps Script
```bash
# This generates google-apps-script/auto-config.gs
npm run setup-google-script

# Copy the generated code to your Google Apps Script
# Run applyAutoConfiguration() function
```

## 📋 Environment Variables Reference

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_GOOGLE_SCRIPT_URL` | Google Apps Script web app URL | `https://script.google.com/macros/s/ABC123/exec` |
| `REACT_APP_GOOGLE_SHEET_ID` | Google Sheet ID from URL | `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms` |
| `REACT_APP_GOOGLE_SCRIPT_API_KEY` | Authentication key | `your-secret-api-key-here` |

### Security Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `REACT_APP_PRODUCTION_DOMAIN` | Your production domain | - | `yourdomain.com` |
| `REACT_APP_ALLOWED_ORIGINS` | Additional allowed origins | - | `https://staging.yourdomain.com,https://preview.yourdomain.com` |
| `NODE_ENV` | Environment mode | `development` | `production` |

### Rate Limiting Variables

| Variable | Description | Default | Range |
|----------|-------------|---------|-------|
| `REACT_APP_MAX_REQUESTS_PER_MINUTE` | Requests per minute | `10` | `1-60` |
| `REACT_APP_MAX_REQUESTS_PER_HOUR` | Requests per hour | `100` | `10-1000` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REACT_APP_GOOGLE_SHEET_NAME` | Sheet tab name | `Waitlist` |

## 🔒 Security Benefits

### 1. **No Hardcoded Secrets**
- API keys are never committed to version control
- Configuration is environment-specific
- Easy to rotate keys without code changes

### 2. **Environment Separation**
- Different configurations for development/staging/production
- Automatic CORS origin management based on environment
- Rate limits can be adjusted per environment

### 3. **Easy Management**
- Single source of truth (`.env` file)
- Automated configuration generation
- No manual editing of Google Apps Script

## 🚀 Deployment Workflows

### Development
```bash
# .env for development
NODE_ENV=development
REACT_APP_PRODUCTION_DOMAIN=localhost
REACT_APP_MAX_REQUESTS_PER_MINUTE=20  # Higher for testing
```

### Staging
```bash
# .env for staging
NODE_ENV=staging
REACT_APP_PRODUCTION_DOMAIN=staging.yourdomain.com
REACT_APP_MAX_REQUESTS_PER_MINUTE=15
```

### Production
```bash
# .env for production
NODE_ENV=production
REACT_APP_PRODUCTION_DOMAIN=yourdomain.com
REACT_APP_MAX_REQUESTS_PER_MINUTE=10
```

## 🔄 Configuration Updates

### Updating Configuration
1. Modify your `.env` file
2. Run `npm run setup-google-script`
3. Copy the new configuration to Google Apps Script
4. Run `applyAutoConfiguration()` function
5. Restart your application

### Rotating API Keys
1. Generate new key: `npm run generate-api-key`
2. Update `.env` file with new key
3. Update Google Apps Script configuration
4. Deploy changes

## 🛠️ Troubleshooting

### Configuration Not Applied
```bash
# Check current Google Apps Script configuration
# In Google Apps Script console, run:
getCurrentConfiguration()
```

### Environment Variables Not Loading
```bash
# Verify .env file exists and has correct format
cat .env

# Restart development server
npm run dev
```

### CORS Issues
```bash
# Check allowed origins in configuration
# Ensure your domain is included in REACT_APP_PRODUCTION_DOMAIN
# or REACT_APP_ALLOWED_ORIGINS
```

## 📝 Best Practices

### 1. **Environment Files**
- Use `.env.local` for local overrides
- Never commit `.env` files to version control
- Use `.env.example` as a template

### 2. **API Key Management**
- Generate strong, unique keys for each environment
- Rotate keys regularly (quarterly recommended)
- Use different keys for development and production

### 3. **Domain Configuration**
- Always use HTTPS in production
- Remove localhost origins from production
- Keep allowed origins list minimal

### 4. **Rate Limiting**
- Set conservative limits for production
- Higher limits for development/testing
- Monitor usage and adjust as needed

## 🔍 Monitoring

### Google Apps Script Logs
Check execution logs for:
- Configuration loading errors
- Rate limit violations
- Authentication failures
- Origin validation issues

### Environment Validation
```bash
# Validate your configuration
npm run setup-google-script

# Check for missing variables
grep -E "^REACT_APP_" .env
```

This environment-based approach provides maximum flexibility, security, and ease of management for your Google Sheets integration! 🎉
