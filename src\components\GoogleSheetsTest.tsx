import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { handleWaitlistSubmission } from '@/utils/googleSheets';
import { toast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

/**
 * Test component for Google Sheets integration
 * This component can be temporarily added to test the integration
 * Remove it once you've verified everything works
 */
export const GoogleSheetsTest: React.FC = () => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTestSubmission = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !firstName) {
      toast({
        title: "Missing fields",
        description: "Please fill in both email and first name",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const testData = {
        email,
        firstName,
        company: 'Test Company',
        role: 'Founder / CXO',
        useCase: 'Sales & revenue analytics',
        dataVolume: '1–50 GB',
        source: 'test',
        consent: true,
      };

      const result = await handleWaitlistSubmission(testData);

      if (result.success) {
        toast({
          title: "Test successful! 🎉",
          description: `Data submitted via ${result.method === 'sheets' ? 'Google Sheets' : 'localStorage fallback'}`,
        });
        setEmail('');
        setFirstName('');
      } else {
        throw new Error('Test submission failed');
      }
    } catch (error) {
      console.error('Test submission error:', error);
      toast({
        title: "Test failed",
        description: "Check the console for error details",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle>Google Sheets Integration Test</CardTitle>
        <CardDescription>
          Use this form to test if your Google Sheets integration is working correctly.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleTestSubmission} className="space-y-4">
          <div>
            <Label htmlFor="test-email">Email</Label>
            <Input
              id="test-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="test-firstName">First Name</Label>
            <Input
              id="test-firstName"
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Test User"
              required
            />
          </div>

          <Button 
            type="submit" 
            disabled={isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                Testing...
              </div>
            ) : (
              'Test Google Sheets Integration'
            )}
          </Button>
        </form>

        <div className="mt-4 p-3 bg-muted rounded-md text-sm">
          <p className="font-medium mb-1">Expected behavior:</p>
          <ul className="text-muted-foreground space-y-1">
            <li>• If Google Sheets is configured: Data goes to your sheet</li>
            <li>• If not configured: Data falls back to localStorage</li>
            <li>• Check your browser console for detailed logs</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
