import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { handleWaitlistSubmission } from '@/utils/googleSheets';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Right } from "lucide-react";
import { toast } from "@/hooks/use-toast";

const waitlistSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  firstName: z.string().min(1, "First name is required"),
  company: z.string().optional(),
  role: z.string().min(1, "Please select your role"),
  useCase: z.string().min(1, "Please select your primary use case"),
  dataVolume: z.string().optional(),
  source: z.string().optional(),
  consent: z.boolean().refine(val => val === true, {
    message: "Please agree to receive product updates"
  })
});

type WaitlistFormData = z.infer<typeof waitlistSchema>;

interface WaitlistFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const roleOptions = [
  "Founder / CXO",
  "Analyst / Data team",
  "Marketing / Growth",
  "Product / Ops",
  "Student / Academic",
  "Other"
];

const useCaseOptions = [
  "Sales & revenue analytics",
  "Marketing performance",
  "Financial / accounting",
  "Supply-chain / ops",
  "Custom dashboards"
];

const dataVolumeOptions = [
  "<1 GB / month",
  "1–50 GB",
  "50 GB+"
];

export const WaitlistForm: React.FC<WaitlistFormProps> = ({ isOpen, onClose }) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [lastSubmissionTime, setLastSubmissionTime] = React.useState<number>(0);

  const form = useForm<WaitlistFormData>({
    resolver: zodResolver(waitlistSchema),
    defaultValues: {
      email: "",
      firstName: "",
      company: "",
      role: "",
      useCase: "",
      dataVolume: "",
      source: "",
      consent: false
    }
  });

  const onSubmit = async (data: WaitlistFormData) => {
    // Prevent rapid double-clicks (debounce)
    const now = Date.now();
    if (now - lastSubmissionTime < 2000) { // 2 second debounce
      console.log('Submission blocked - too soon after last attempt');
      return;
    }

    setLastSubmissionTime(now);
    setIsSubmitting(true);

    try {
      const result = await handleWaitlistSubmission(data);

      if (result.success) {
        toast({
          title: "Welcome to the DataGent waitlist! 🎉",
          description: `Thanks ${data.firstName}! We'll notify you as soon as DataGent is available. Check your email for confirmation.`,
        });

        // Optional: Show different message based on submission method
        if (result.method === 'local') {
          console.log('Data stored locally - Google Sheets integration not configured');
        }

        form.reset();
        onClose();
      } else {
        // Handle specific error cases
        const errorMessage = result.error || 'Failed to submit waitlist data';

        toast({
          title: "Unable to submit",
          description: errorMessage,
          variant: "destructive",
        });

        // Don't close the form on error so user can try again
        // but the rate limiting fix prevents the loop
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      toast({
        title: "Something went wrong",
        description: "Please try again in a moment.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center gradient-text">
            Join the DataGent Waitlist
          </DialogTitle>
          <DialogDescription className="text-center text-muted-foreground">
            Get early access to AI that actually works for your business. Takes just 30 seconds.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Email - Required */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address *</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="<EMAIL>" 
                      type="email"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* First Name - Required */}
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Company - Optional */}
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company / Organization</FormLabel>
                  <FormControl>
                    <Input placeholder="Your company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Role - Required Dropdown */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {roleOptions.map((role) => (
                        <SelectItem key={role} value={role}>
                          {role}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Primary Use Case - Required Dropdown */}
            <FormField
              control={form.control}
              name="useCase"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Use Case *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="What will you use DataGent for?" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {useCaseOptions.map((useCase) => (
                        <SelectItem key={useCase} value={useCase}>
                          {useCase}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data Volume - Optional */}
            <FormField
              control={form.control}
              name="dataVolume"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data Volume You Work With</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select data volume (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {dataVolumeOptions.map((volume) => (
                        <SelectItem key={volume} value={volume}>
                          {volume}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Source - Hidden/Optional */}
            <FormField
              control={form.control}
              name="source"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How did you hear about us?</FormLabel>
                  <FormControl>
                    <Input placeholder="Twitter, LinkedIn, referral, etc." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Consent Checkbox - Required */}
            <FormField
              control={form.control}
              name="consent"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-sm">
                      I agree to receive product updates and early access notifications *
                    </FormLabel>
                    <FormDescription className="text-xs">
                      We respect your privacy. You can unsubscribe anytime.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <FormMessage />

            {/* Submit Button */}
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full h-12 gradient-cta text-white font-semibold text-lg group"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" className="mr-2" />
                  Joining Waitlist...
                </div>
              ) : (
                <>
                  <Rocket className="mr-2 w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
                  Join Waitlist
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </>
              )}
            </Button>

            {/* Benefits Reminder */}
            <div className="flex flex-wrap items-center justify-center gap-4 pt-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Early access</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Special pricing</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Free to join</span>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};