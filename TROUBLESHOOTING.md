# Troubleshooting Guide: "Too Many Submissions" Error

## 🚨 Quick Fixes

### 1. **Clear Rate Limit Data (Immediate Fix)**
Open your browser's developer console and run:
```javascript
// Clear client-side rate limiting
localStorage.removeItem('waitlist_submissions');
console.log('Rate limit cleared');

// Check current status
import { debugRateLimit } from './src/utils/googleSheets';
debugRateLimit();
```

### 2. **Use the Debug Panel**
- Look for the "Debug" button in the bottom-right corner of your page (development mode only)
- Click "Clear Rate Limit" to reset client-side limits
- Click "Check Rate Limit" to see current status

### 3. **Check Google Apps Script Configuration**
Run this command to verify your Google Apps Script setup:
```bash
npm run setup-google-script
```

## 🔍 Root Cause Analysis

### Issue 1: Rate Limiting Conflicts
You have multiple rate limiting layers:
- **Client-side**: 5 submissions per hour per browser
- **Server-side**: 10 per minute, 100 per hour (Google Apps Script)

### Issue 2: Google Apps Script Not Configured
Your Google Apps Script might not be properly configured with environment variables.

### Issue 3: CORS/Network Issues
The "Failed to load resource" errors suggest network connectivity problems.

## 🛠️ Step-by-Step Solutions

### Solution 1: Reset Everything
```bash
# 1. Clear browser data
# Open DevTools → Application → Storage → Clear storage

# 2. Regenerate API key
npm run generate-api-key

# 3. Reconfigure Google Apps Script
npm run setup-google-script
```

### Solution 2: Check Environment Variables
Verify your `.env` file has these values:
```env
REACT_APP_GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
REACT_APP_GOOGLE_SCRIPT_API_KEY=your-api-key-here
REACT_APP_GOOGLE_SHEET_ID=your-sheet-id-here
```

### Solution 3: Test Google Apps Script Directly
1. Open your Google Apps Script project
2. Go to the execution log
3. Run the `setupConfiguration()` function manually
4. Check for any error messages

### Solution 4: Verify Google Apps Script Deployment
1. In Google Apps Script, click "Deploy" → "Manage deployments"
2. Ensure it's deployed as a "Web app"
3. Execution should be set to "Anyone"
4. Access should be "Anyone, even anonymous"

## 🔧 Advanced Debugging

### Check Rate Limit Status
```javascript
// In browser console
localStorage.getItem('waitlist_submissions')
```

### Monitor Network Requests
1. Open DevTools → Network tab
2. Try submitting the form
3. Look for the request to your Google Apps Script URL
4. Check the response status and error messages

### Google Apps Script Logs
1. Open your Google Apps Script project
2. Click "Executions" in the left sidebar
3. Look for recent executions and any error messages

## 🚀 Prevention

### 1. Increase Rate Limits (if needed)
Edit `src/utils/googleSheets.ts`:
```typescript
const MAX_SUBMISSIONS_PER_HOUR = 10; // Increase from 5
```

### 2. Add Better Error Messages
The system now includes more detailed error logging in the browser console.

### 3. Monitor Submissions
Use the debug panel to monitor submission patterns and identify issues early.

## 📞 Still Having Issues?

If you're still experiencing problems:

1. **Check the browser console** for detailed error messages
2. **Use the debug panel** to inspect rate limit status
3. **Verify your Google Apps Script** is properly deployed
4. **Test with a fresh browser session** (incognito mode)

## 🎯 Expected Behavior

- **Normal operation**: Forms submit successfully to Google Sheets
- **Rate limiting**: Clear error message with time until reset
- **Fallback**: Data stored locally if Google Sheets fails
- **No data loss**: All submissions are preserved either in Sheets or locally
